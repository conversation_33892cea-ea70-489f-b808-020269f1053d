<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{TITLE}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .chat-container {
            width: 100%;
            max-width: 800px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .model-name {
            font-size: 14px;
            opacity: 0.9;
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 12px;
            border-radius: 12px;
            display: inline-block;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            line-height: 1.4;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e1e5e9;
            border-bottom-left-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            margin: 0 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            order: 1;
        }

        .message.assistant .message-avatar {
            background: #f0f0f0;
            color: #666;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e1e5e9;
        }

        .chat-input-wrapper {
            display: flex;
            align-items: flex-end;
            background: #f8f9fa;
            border-radius: 24px;
            padding: 8px;
            border: 2px solid transparent;
            transition: border-color 0.2s ease;
        }

        .chat-input-wrapper:focus-within {
            border-color: #667eea;
        }

        .chat-input {
            flex: 1;
            border: none;
            outline: none;
            background: transparent;
            padding: 12px 16px;
            font-size: 16px;
            resize: none;
            max-height: 120px;
            min-height: 20px;
            line-height: 1.4;
        }

        .send-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            margin-left: 8px;
        }

        .send-button:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .send-button:active {
            transform: scale(0.95);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: none;
            padding: 12px 16px;
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 18px;
            border-bottom-left-radius: 4px;
            max-width: 70%;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .typing-dots {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #999;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .welcome-message {
            text-align: center;
            color: #666;
            padding: 40px 20px;
            font-size: 16px;
        }

        .welcome-title {
            font-size: 24px;
            color: #333;
            margin-bottom: 10px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .chat-container {
                height: 95vh;
                border-radius: 15px;
            }

            .message-content {
                max-width: 85%;
            }

            .chat-title {
                font-size: 20px;
            }
        }

        /* 滚动条样式 */
        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- 聊天头部 -->
        <div class="chat-header">
            <div class="chat-title">{{TITLE}}</div>
            <div class="model-name">{{MODEL_NAME}}</div>
        </div>

        <!-- 消息显示区域 -->
        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                <div class="welcome-title">欢迎使用 {{MODEL_NAME}}</div>
                <p>开始与AI助手对话吧！输入您的问题或想法。</p>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input-container">
            <div class="chat-input-wrapper">
                <textarea
                    id="chatInput"
                    class="chat-input"
                    placeholder="输入您的消息..."
                    rows="1"
                ></textarea>
                <button id="sendButton" class="send-button">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="22" y1="2" x2="11" y2="13"></line>
                        <polygon points="22,2 15,22 11,13 2,9"></polygon>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        class ChatInterface {
            constructor() {
                this.chatMessages = document.getElementById('chatMessages');
                this.chatInput = document.getElementById('chatInput');
                this.sendButton = document.getElementById('sendButton');
                this.messageCount = 0;

                this.init();
            }

            init() {
                // 绑定事件监听器
                this.sendButton.addEventListener('click', () => this.sendMessage());
                this.chatInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                // 自动调整输入框高度
                this.chatInput.addEventListener('input', () => {
                    this.autoResizeTextarea();
                });

                // 清除欢迎消息（当有消息时）
                this.shouldClearWelcome = true;
            }

            autoResizeTextarea() {
                this.chatInput.style.height = 'auto';
                this.chatInput.style.height = Math.min(this.chatInput.scrollHeight, 120) + 'px';
            }

            sendMessage() {
                const message = this.chatInput.value.trim();
                if (!message) return;

                // 清除欢迎消息
                if (this.shouldClearWelcome) {
                    this.chatMessages.innerHTML = '';
                    this.shouldClearWelcome = false;
                }

                // 添加用户消息
                this.addMessage(message, 'user');

                // 清空输入框
                this.chatInput.value = '';
                this.chatInput.style.height = 'auto';

                // 禁用发送按钮
                this.sendButton.disabled = true;

                // 显示打字指示器
                this.showTypingIndicator();

                // 模拟AI回复
                setTimeout(() => {
                    this.hideTypingIndicator();
                    this.addMessage(this.generateAIResponse(message), 'assistant');
                    this.sendButton.disabled = false;
                    this.chatInput.focus();
                }, 1000 + Math.random() * 2000); // 1-3秒随机延迟
            }

            addMessage(content, sender) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;

                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';
                avatar.textContent = sender === 'user' ? '您' : 'AI';

                const messageContent = document.createElement('div');
                messageContent.className = 'message-content';
                messageContent.textContent = content;

                if (sender === 'user') {
                    messageDiv.appendChild(messageContent);
                    messageDiv.appendChild(avatar);
                } else {
                    messageDiv.appendChild(avatar);
                    messageDiv.appendChild(messageContent);
                }

                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();
                this.messageCount++;
            }

            showTypingIndicator() {
                const typingDiv = document.createElement('div');
                typingDiv.className = 'message assistant';
                typingDiv.id = 'typingIndicator';

                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';
                avatar.textContent = 'AI';

                const typingContent = document.createElement('div');
                typingContent.className = 'typing-indicator';
                typingContent.style.display = 'block';
                typingContent.innerHTML = `
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                `;

                typingDiv.appendChild(avatar);
                typingDiv.appendChild(typingContent);
                this.chatMessages.appendChild(typingDiv);
                this.scrollToBottom();
            }

            hideTypingIndicator() {
                const typingIndicator = document.getElementById('typingIndicator');
                if (typingIndicator) {
                    typingIndicator.remove();
                }
            }

            generateAIResponse(userMessage) {
                // 简单的AI回复生成器（实际应用中应该连接到真实的AI模型）
                const responses = [
                    `我理解您提到的"${userMessage}"。这是一个很有趣的话题，让我为您详细解答。`,
                    `关于"${userMessage}"，我可以为您提供以下信息和建议。`,
                    `感谢您的问题"${userMessage}"。基于我的知识，我认为...`,
                    `这是一个很好的问题！关于"${userMessage}"，我想分享一些见解。`,
                    `让我来帮您分析一下"${userMessage}"这个问题。`
                ];

                const randomResponse = responses[Math.floor(Math.random() * responses.length)];

                // 添加一些随机的详细内容
                const details = [
                    "首先，我们需要考虑多个方面的因素。",
                    "从技术角度来看，这涉及到几个重要的概念。",
                    "根据最新的研究和实践经验，我建议采用以下方法。",
                    "这个问题确实需要仔细分析，让我逐步为您解释。"
                ];

                return randomResponse + " " + details[Math.floor(Math.random() * details.length)];
            }

            scrollToBottom() {
                setTimeout(() => {
                    this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
                }, 100);
            }
        }

        // 初始化聊天界面
        document.addEventListener('DOMContentLoaded', () => {
            new ChatInterface();
        });
    </script>
</body>
</html>
